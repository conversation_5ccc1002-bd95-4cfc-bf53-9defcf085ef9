/**
 * Главный модуль для формы редактирования платежа работнику
 * Координирует работу всех модулей редактирования
 */

// Глобальный объект для приложения редактирования
window.WorkerPaymentEditApp = {

    constants: {},
    isInitialized: false,

    /**
     * Инициализация приложения редактирования с константами
     */
    ready: function (constants) {
        this.constants = constants;

        // Устанавливаем глобальные константы
        for (var key in constants) {
            if (constants.hasOwnProperty(key)) {
                window[key] = constants[key];
            }
        }

        this.init();
    },

    /**
     * Основная инициализация
     */
    init: function () {
        if (this.isInitialized) return;

        console.log('Инициализация Worker Payment Edit App...');

        // Инициализируем все модули
        this.initializeModules();

        // Устанавливаем обработчики событий
        this.setupEventHandlers();

        // Инициализируем UI
        this.initializeUI();

        this.isInitialized = true;
        console.log('Worker Payment Edit App инициализирован успешно');
    },

    /**
     * Инициализация всех модулей
     */
    initializeModules: function () {
        // Инициализируем обработчики событий
        if (typeof window.WorkerPaymentEditHandlers !== 'undefined') {
            window.WorkerPaymentEditHandlers.init();
        }

        // Другие модули инициализируются по мере необходимости
    },

    /**
     * Настройка глобальных обработчиков событий
     */
    setupEventHandlers: function () {
        var self = this;

        // Обработчик изменения данных формы
        $(document).on('input change', '#worker-payment-edit-form input, #worker-payment-edit-form select', function () {
            self.onFormDataChange();
        });

        // Обработчик успешного обновления
        $(document).on('paymentUpdated', function () {
            self.onPaymentUpdated();
        });
    },

    /**
     * Инициализация UI элементов
     */
    initializeUI: function () {
        // Инициализация Select2 для селектов в форме редактирования
        this.initializeSelect2();

        // Показ/скрытие секций в зависимости от данных
        this.updateUIVisibility();
    },

    /**
     * Инициализация Select2
     */
    initializeSelect2: function () {
        $('.select2').each(function () {
            if (!$(this).hasClass('select2-hidden-accessible')) {
                var parentModal = $(this).closest('.modal');
                $(this).select2({
                    width: '100%',
                    language: {
                        noResults: function () {
                            return "Результаты не найдены";
                        },
                        searching: function () {
                            return "Поиск...";
                        }
                    },
                    allowClear: true,
                    placeholder: function () {
                        return $(this).find('option:first').text();
                    },
                    dropdownParent: parentModal.length ? parentModal : $(document.body)
                });
            }
        });
    },

    /**
     * Обновление видимости UI элементов
     */
    updateUIVisibility: function () {
        // Показываем/скрываем секцию долга в зависимости от наличия долга
        if (typeof WORKER_DEBT !== 'undefined' && WORKER_DEBT > 0) {
            $('#debt-payment-section').show();
        }
    },

    /**
     * Обработчик изменения данных формы
     */
    onFormDataChange: function () {
        // Можно добавить здесь автоматические вычисления
        // или обновление UI в реальном времени
    },

    /**
     * Обработчик успешного обновления платежа
     */
    onPaymentUpdated: function () {
        console.log('Платеж успешно обновлен');

        // Можно добавить дополнительную логику после обновления
        // например, очистку кэша или обновление других частей интерфейса
    },

    /**
     * Получение текущих данных формы
     */
    getFormData: function () {
        if (typeof window.WorkerPaymentEditAjax !== 'undefined') {
            return window.WorkerPaymentEditAjax.prepareFormData();
        }
        return {};
    },

    /**
     * Валидация формы
     */
    validateForm: function () {
        if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
            return window.WorkerPaymentEditValidation.validateForm();
        }
        return true;
    },

    /**
     * Отправка формы
     */
    submitForm: function () {
        if (this.validateForm()) {
            var formData = this.getFormData();
            if (typeof window.WorkerPaymentEditAjax !== 'undefined') {
                window.WorkerPaymentEditAjax.updatePayment(formData);
            }
        }
    },

    /**
     * Сброс формы к исходному состоянию
     */
    resetForm: function () {
        $('#worker-payment-edit-form')[0].reset();

        if (typeof window.WorkerPaymentEditUI !== 'undefined') {
            window.WorkerPaymentEditUI.clearValidationErrors();
        }

        // Сброс чекбоксов и состояний
        $('.payment-type-checkbox').prop('checked', false).trigger('change');
        $('#debt-payment-checkbox').prop('checked', false).trigger('change');
    },

    /**
     * Деинициализация (очистка)
     */
    destroy: function () {
        // Очищаем обработчики событий
        $(document).off('input change', '#worker-payment-edit-form input, #worker-payment-edit-form select');
        $(document).off('paymentUpdated');

        // Очищаем Select2
        $('.select2').select2('destroy');

        this.isInitialized = false;
    }
};

// Автоматическая инициализация отключена - инициализация происходит из index.php
// $(document).ready(function () {
//     // Инициализируем только если форма редактирования присутствует
//     if ($('#worker-payment-edit-form').length > 0) {
//         // Если константы уже заданы, инициализируем сразу
//         if (typeof WORKER_FINANCES_TYPE_SALARY !== 'undefined') {
//             window.WorkerPaymentEditApp.init();
//         }
//     }
// });
