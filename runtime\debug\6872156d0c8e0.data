a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:34528:"a:1:{s:8:"messages";a:36:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307052.881329;i:4;a:0:{}i:5;i:2640376;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307052.884249;i:4;a:0:{}i:5;i:2758312;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307052.885372;i:4;a:0:{}i:5;i:2799520;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307052.885388;i:4;a:0:{}i:5;i:2799896;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752307052.910088;i:4;a:0:{}i:5;i:3945096;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752307052.92447;i:4;a:0:{}i:5;i:4755584;}i:6;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752307052.946451;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6060232;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307052.994172;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6197656;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.028625;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6307976;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.036973;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6602744;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307053.050772;i:4;a:0:{}i:5;i:7293072;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752307053.054833;i:4;a:0:{}i:5;i:8076984;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752307053.055198;i:4;a:0:{}i:5;i:8101816;}i:45;a:6:{i:0;s:46:"Route requested: 'backend/worker-payment/edit'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752307053.055855;i:4;a:0:{}i:5;i:8156744;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752307053.055862;i:4;a:0:{}i:5;i:8158376;}i:47;a:6:{i:0;s:41:"Route to run: backend/worker-payment/edit";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752307053.057799;i:4;a:0:{}i:5;i:8287512;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.062316;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8768088;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.066239;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8774704;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1752307053.069395;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8778080;}i:55;a:6:{i:0;s:85:"Running action: app\modules\backend\controllers\WorkerPaymentController::actionEdit()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752307053.069421;i:4;a:0:{}i:5;i:8777232;}i:56;a:6:{i:0;s:86:"SELECT "id", "full_name" FROM "worker" WHERE "deleted_at" IS NULL ORDER BY "full_name"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.071173;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:31;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8932944;}i:59;a:6:{i:0;s:67:"SELECT * FROM "cashbox" WHERE "deleted_at" IS NULL ORDER BY "title"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.075535;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9190848;}i:62;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'cashbox'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.078059;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9206216;}i:65;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='cashbox'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.083065;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222024;}i:68;a:6:{i:0;s:2812:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.086763;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9256296;}i:71;a:6:{i:0;s:874:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.091575;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9266416;}i:74;a:6:{i:0;s:36:"SELECT * FROM "worker" WHERE "id"=91";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.09497;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9271672;}i:77;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.096568;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9289544;}i:80;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.099186;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9304064;}i:83;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.104104;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9314176;}i:86;a:6:{i:0;s:82:"SELECT * FROM "worker_salary" WHERE ("worker_id"=91) AND ("end_date"='9999-12-31')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.108077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9357608;}i:89;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_salary'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.110814;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9368840;}i:92;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_salary'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.115854;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9378512;}i:95;a:6:{i:0;s:106:"SELECT SUM(amount) FROM "worker_debt" WHERE ("worker_id"=91) AND ("amount" > 0) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.120207;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:91;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9483920;}i:98;a:6:{i:0;s:108:"SELECT SUM(amount) FROM "worker_finances" WHERE (("worker_id"=91) AND ("type"=6)) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.122947;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:96;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9489040;}i:101;a:6:{i:0;s:97:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\worker-payment\edit.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752307053.126324;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:398;s:8:"function";s:13:"renderPartial";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9618336;}}}";s:9:"profiling";s:63394:"a:3:{s:6:"memory";i:10369328;s:4:"time";d:0.27189111709594727;s:8:"messages";a:42:{i:7;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752307052.946493;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6061360;}i:8;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752307052.992919;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6063296;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307052.994221;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6199232;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.026684;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214592;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.028671;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309464;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.032801;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6311024;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.037015;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6604088;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.04156;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6607096;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.06234;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8770696;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.06564;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8772856;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.066262;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8777312;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.068864;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8779408;}i:57;a:6:{i:0;s:86:"SELECT "id", "full_name" FROM "worker" WHERE "deleted_at" IS NULL ORDER BY "full_name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.071194;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:31;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8935184;}i:58;a:6:{i:0;s:86:"SELECT "id", "full_name" FROM "worker" WHERE "deleted_at" IS NULL ORDER BY "full_name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.073499;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:31;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8980904;}i:60;a:6:{i:0;s:67:"SELECT * FROM "cashbox" WHERE "deleted_at" IS NULL ORDER BY "title"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.07556;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9193088;}i:61;a:6:{i:0;s:67:"SELECT * FROM "cashbox" WHERE "deleted_at" IS NULL ORDER BY "title"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.077691;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9197288;}i:63;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'cashbox'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.078091;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9208456;}i:64;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'cashbox'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.082572;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9221448;}i:66;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='cashbox'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.083089;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9224264;}i:67;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='cashbox'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.085899;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9226576;}i:69;a:6:{i:0;s:2812:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.086794;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9258160;}i:70;a:6:{i:0;s:2812:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.091115;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273848;}i:72;a:6:{i:0;s:874:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.0916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9268280;}i:73;a:6:{i:0;s:874:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.094575;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9271104;}i:75;a:6:{i:0;s:36:"SELECT * FROM "worker" WHERE "id"=91";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.094992;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9274656;}i:76;a:6:{i:0;s:36:"SELECT * FROM "worker" WHERE "id"=91";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.095607;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277272;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.09659;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9292632;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.098828;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298608;}i:81;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.099217;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9305928;}i:82;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.103798;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9321624;}i:84;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.104127;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9316040;}i:85;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.106944;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9318864;}i:87;a:6:{i:0;s:82:"SELECT * FROM "worker_salary" WHERE ("worker_id"=91) AND ("end_date"='9999-12-31')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.1081;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9360648;}i:88;a:6:{i:0;s:82:"SELECT * FROM "worker_salary" WHERE ("worker_id"=91) AND ("end_date"='9999-12-31')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.110463;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9363296;}i:90;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_salary'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.11085;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9370704;}i:91;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_salary'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.115431;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9384832;}i:93;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_salary'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.115879;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9380376;}i:94;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_salary'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.118817;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9383200;}i:96;a:6:{i:0;s:106:"SELECT SUM(amount) FROM "worker_debt" WHERE ("worker_id"=91) AND ("amount" > 0) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.120234;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:91;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9487008;}i:97;a:6:{i:0;s:106:"SELECT SUM(amount) FROM "worker_debt" WHERE ("worker_id"=91) AND ("amount" > 0) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.122639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:91;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9488728;}i:99;a:6:{i:0;s:108:"SELECT SUM(amount) FROM "worker_finances" WHERE (("worker_id"=91) AND ("type"=6)) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.122985;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:96;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9492128;}i:100;a:6:{i:0;s:108:"SELECT SUM(amount) FROM "worker_finances" WHERE (("worker_id"=91) AND ("type"=6)) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.123667;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:96;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9493848;}}}";s:2:"db";s:62559:"a:1:{s:8:"messages";a:40:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307052.994221;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6199232;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.026684;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214592;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.028671;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309464;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.032801;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6311024;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.037015;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6604088;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.04156;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6607096;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.06234;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8770696;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.06564;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8772856;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.066262;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8777312;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.068864;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8779408;}i:57;a:6:{i:0;s:86:"SELECT "id", "full_name" FROM "worker" WHERE "deleted_at" IS NULL ORDER BY "full_name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.071194;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:31;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8935184;}i:58;a:6:{i:0;s:86:"SELECT "id", "full_name" FROM "worker" WHERE "deleted_at" IS NULL ORDER BY "full_name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.073499;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:31;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8980904;}i:60;a:6:{i:0;s:67:"SELECT * FROM "cashbox" WHERE "deleted_at" IS NULL ORDER BY "title"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.07556;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9193088;}i:61;a:6:{i:0;s:67:"SELECT * FROM "cashbox" WHERE "deleted_at" IS NULL ORDER BY "title"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.077691;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9197288;}i:63;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'cashbox'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.078091;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9208456;}i:64;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'cashbox'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.082572;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9221448;}i:66;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='cashbox'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.083089;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9224264;}i:67;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='cashbox'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.085899;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentService.php";s:4:"line";i:38;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:27;s:8:"function";s:11:"getFormData";s:5:"class";s:64:"app\modules\backend\services\worker_payment\WorkerPaymentService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9226576;}i:69;a:6:{i:0;s:2812:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.086794;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9258160;}i:70;a:6:{i:0;s:2812:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.091115;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273848;}i:72;a:6:{i:0;s:874:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.0916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9268280;}i:73;a:6:{i:0;s:874:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.094575;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9271104;}i:75;a:6:{i:0;s:36:"SELECT * FROM "worker" WHERE "id"=91";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.094992;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9274656;}i:76;a:6:{i:0;s:36:"SELECT * FROM "worker" WHERE "id"=91";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.095607;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:30;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277272;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.09659;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9292632;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.098828;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298608;}i:81;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.099217;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9305928;}i:82;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.103798;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9321624;}i:84;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.104127;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9316040;}i:85;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.106944;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:42;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9318864;}i:87;a:6:{i:0;s:82:"SELECT * FROM "worker_salary" WHERE ("worker_id"=91) AND ("end_date"='9999-12-31')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.1081;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9360648;}i:88;a:6:{i:0;s:82:"SELECT * FROM "worker_salary" WHERE ("worker_id"=91) AND ("end_date"='9999-12-31')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.110463;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9363296;}i:90;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_salary'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.11085;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9370704;}i:91;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_salary'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.115431;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9384832;}i:93;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_salary'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.115879;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9380376;}i:94;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_salary'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.118817;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:84;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9383200;}i:96;a:6:{i:0;s:106:"SELECT SUM(amount) FROM "worker_debt" WHERE ("worker_id"=91) AND ("amount" > 0) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.120234;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:91;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9487008;}i:97;a:6:{i:0;s:106:"SELECT SUM(amount) FROM "worker_debt" WHERE ("worker_id"=91) AND ("amount" > 0) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.122639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:91;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9488728;}i:99;a:6:{i:0;s:108:"SELECT SUM(amount) FROM "worker_finances" WHERE (("worker_id"=91) AND ("type"=6)) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.122985;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:96;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9492128;}i:100;a:6:{i:0;s:108:"SELECT SUM(amount) FROM "worker_finances" WHERE (("worker_id"=91) AND ("type"=6)) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752307053.123667;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:96;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:390;s:8:"function";s:15:"getEditFormData";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9493848;}}}";s:5:"event";s:10563:"a:57:{i:0;a:5:{s:4:"time";d:1752307052.939834;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752307052.992904;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752307053.041962;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1752307053.042021;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1752307053.047941;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1752307053.055391;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752307053.057901;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752307053.057912;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1752307053.060383;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752307053.060404;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752307053.060413;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752307053.060419;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752307053.060425;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752307053.06043;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752307053.060436;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752307053.060469;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:55:"app\modules\backend\controllers\WorkerPaymentController";}i:16;a:5:{s:4:"time";d:1752307053.071083;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752307053.07501;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:18;a:5:{s:4:"time";d:1752307053.075445;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:1752307053.077993;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:20;a:5:{s:4:"time";d:1752307053.086239;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:21;a:5:{s:4:"time";d:1752307053.086256;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:22;a:5:{s:4:"time";d:1752307053.086268;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:23;a:5:{s:4:"time";d:1752307053.086272;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:24;a:5:{s:4:"time";d:1752307053.086275;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Cashbox";}i:25;a:5:{s:4:"time";d:1752307053.086706;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:1752307053.095878;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\models\Worker";}i:27;a:5:{s:4:"time";d:1752307053.095916;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\models\Worker";}i:28;a:5:{s:4:"time";d:1752307053.095936;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:1752307053.099126;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:30;a:5:{s:4:"time";d:1752307053.107342;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:31;a:5:{s:4:"time";d:1752307053.107363;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:32;a:5:{s:4:"time";d:1752307053.107376;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:33;a:5:{s:4:"time";d:1752307053.107385;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:34;a:5:{s:4:"time";d:1752307053.107395;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:35;a:5:{s:4:"time";d:1752307053.107404;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:36;a:5:{s:4:"time";d:1752307053.107416;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:37;a:5:{s:4:"time";d:1752307053.10742;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:38;a:5:{s:4:"time";d:1752307053.107423;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:39;a:5:{s:4:"time";d:1752307053.107427;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:40;a:5:{s:4:"time";d:1752307053.10743;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:41;a:5:{s:4:"time";d:1752307053.107433;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:42;a:5:{s:4:"time";d:1752307053.107437;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:43;a:5:{s:4:"time";d:1752307053.107962;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:44;a:5:{s:4:"time";d:1752307053.110747;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\WorkerSalary";}i:45;a:5:{s:4:"time";d:1752307053.119165;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\WorkerSalary";}i:46;a:5:{s:4:"time";d:1752307053.119528;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:1752307053.122819;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:1752307053.126311;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:49;a:5:{s:4:"time";d:1752307053.134342;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:50;a:5:{s:4:"time";d:1752307053.134407;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:55:"app\modules\backend\controllers\WorkerPaymentController";}i:51;a:5:{s:4:"time";d:1752307053.13442;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:52;a:5:{s:4:"time";d:1752307053.134429;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:53;a:5:{s:4:"time";d:1752307053.134444;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:54;a:5:{s:4:"time";d:1752307053.134454;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:55;a:5:{s:4:"time";d:1752307053.136558;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:56;a:5:{s:4:"time";d:1752307053.136895;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752307052.866957;s:3:"end";d:1752307053.139496;s:6:"memory";i:10369328;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4872:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055732;i:4;a:0:{}i:5;i:8138112;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055745;i:4;a:0:{}i:5;i:8138864;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.05575;i:4;a:0:{}i:5;i:8139616;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055754;i:4;a:0:{}i:5;i:8140368;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055758;i:4;a:0:{}i:5;i:8141120;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055763;i:4;a:0:{}i:5;i:8141872;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055766;i:4;a:0:{}i:5;i:8142624;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.05577;i:4;a:0:{}i:5;i:8143376;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055774;i:4;a:0:{}i:5;i:8144128;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055778;i:4;a:0:{}i:5;i:8144880;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055782;i:4;a:0:{}i:5;i:8145632;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055786;i:4;a:0:{}i:5;i:8146384;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055791;i:4;a:0:{}i:5;i:8148416;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055795;i:4;a:0:{}i:5;i:8149168;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055798;i:4;a:0:{}i:5;i:8149920;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055802;i:4;a:0:{}i:5;i:8150672;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055806;i:4;a:0:{}i:5;i:8151424;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055811;i:4;a:0:{}i:5;i:8152176;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055815;i:4;a:0:{}i:5;i:8152928;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055819;i:4;a:0:{}i:5;i:8153680;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055823;i:4;a:0:{}i:5;i:8154432;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055827;i:4;a:0:{}i:5;i:8155184;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752307053.055841;i:4;a:0:{}i:5;i:8157256;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752307053.055845;i:4;a:0:{}i:5;i:8157096;}}s:5:"route";s:27:"backend/worker-payment/edit";s:6:"action";s:69:"app\modules\backend\controllers\WorkerPaymentController::actionEdit()";}";s:7:"request";s:5914:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:10:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:12:"x-csrf-token";s:88:"bnd4gYnVkGrax5usPJeP68tauIThyY9JQ1MSL7Xs1OsfQSys7aH2ILWN9MNS0-K9nTH1ypiM5x0UGiNkz9iXkQ==";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:7:"referer";s:42:"http://silver/backend/worker-payment/index";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6872156d0c8e0";s:16:"X-Debug-Duration";s:3:"271";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6872156d0c8e0";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Mon, 11-Aug-2025 07:57:33 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:27:"backend/worker-payment/edit";s:6:"action";s:69:"app\modules\backend\controllers\WorkerPaymentController::actionEdit()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:1;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:40:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:17:"HTTP_X_CSRF_TOKEN";s:88:"bnd4gYnVkGrax5usPJeP68tauIThyY9JQ1MSL7Xs1OsfQSys7aH2ILWN9MNS0-K9nTH1ypiM5x0UGiNkz9iXkQ==";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"HTTP_REFERER";s:42:"http://silver/backend/worker-payment/index";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"50062";s:12:"REDIRECT_URL";s:28:"/backend/worker-payment/edit";s:21:"REDIRECT_QUERY_STRING";s:26:"worker_id=91&month=2025-07";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:26:"worker_id=91&month=2025-07";s:11:"REQUEST_URI";s:55:"/backend/worker-payment/edit?worker_id=91&month=2025-07";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752307052.849008;s:12:"REQUEST_TIME";i:1752307052;}s:3:"GET";a:2:{s:9:"worker_id";s:2:"91";s:5:"month";s:7:"2025-07";}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"tg16mblupj6u875l5vqjrkigi7pqr8f3";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a:2:{i:0;s:5:"_csrf";i:1;s:32:"q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1752314253;s:13:"last_activity";i:1752307053;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'Am4PA_XsYEchREdnLU19DkEKnev9Tqn03HNbrXNd'";s:8:"password";s:62:"'$2y$13$0eRlRvvyoXFPe1EZQr1a1OJtiuhoGEzSLGqLV0XWtF3ZVbwKTSkjO'";s:10:"created_at";s:21:"'2025-02-25 12:53:18'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:839:"a:2:{s:23:"app\assets\Select2Asset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:70:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js";}s:3:"css";a:1:{i:0;s:72:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"6872156d0c8e0";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752307052.849008;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10369328;s:14:"processingTime";d:0.27189111709594727;}s:10:"exceptions";a:0:{}}