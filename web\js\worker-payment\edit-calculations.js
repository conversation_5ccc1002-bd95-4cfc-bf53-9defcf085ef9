/**
 * Модуль вычислений для формы редактирования платежа работнику
 * Содержит функции для математических операций и форматирования
 */

// Глобальный объект для функций вычислений редактирования
window.WorkerPaymentEditCalculations = {

    /**
     * Форматирование числа с разделителями тысяч
     */
    formatNumber: function (number) {
        if (!number || isNaN(number)) return '';
        return parseFloat(number).toLocaleString('ru-RU');
    },

    /**
     * Получение числового значения из строки с любой группировкой разрядов
     * Удаляем ВСЕ символы, кроме цифр, чтобы избежать ошибок при наличии
     * узких неразрывных пробелов (U+202F), обычных пробелов, запятых и др.
     */
    getNumericValue: function (formattedString) {
        if (!formattedString) return 0;

        // Удаляем все символы, которые НЕ являются цифрами
        var numericString = formattedString.toString().replace(/[^\d]/g, '');

        // Преобразуем в целое число
        var number = parseInt(numericString || '0', 10);

        return isNaN(number) ? 0 : number;
    },

    /**
     * Подсчет общей суммы всех выплат
     */
    calculateTotalAmount: function () {
        var total = 0;

        // Суммируем основные типы платежей
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var checkedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                // Суммируем из динамических полей
                dynamicAmounts.find('input[data-method]').each(function () {
                    var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    total += amount;
                });
            } else {
                // Берем из основного поля
                var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                total += amount;
            }
        });

        // Добавляем погашение долга
        if ($('#debt-payment-checkbox').prop('checked')) {
            var debtAmount = window.WorkerPaymentEditCalculations.getNumericValue($('#debt-payment-amount').val());
            total += debtAmount;
        }

        return total;
    },

    /**
     * Подсчет суммы зарплаты и аванса
     */
    calculateSalaryAndAdvanceTotal: function () {
        var total = 0;

        var salaryRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_SALARY + '"]');
        var advanceRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_ADVANCE + '"]');

        [salaryRow, advanceRow].forEach(function (row) {
            if (row.length > 0) {
                var typeCheckbox = row.find('.payment-type-checkbox');

                if (typeCheckbox.prop('checked')) {
                    var checkedMethods = row.find('.payment-method-checkbox:checked');
                    var amountInput = row.find('.amount-input');
                    var dynamicAmounts = row.find('.dynamic-amounts');

                    if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                        dynamicAmounts.find('input[data-method]').each(function () {
                            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                            total += amount;
                        });
                    } else {
                        var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                        total += amount;
                    }
                }
            }
        });

        return total;
    },

    /**
     * Подсчет оставшейся зарплаты
     */
    calculateRemainingSalary: function () {
        if (typeof WORKER_SALARY === 'undefined') return 0;

        var totalSalaryAndAdvance = this.calculateSalaryAndAdvanceTotal();
        return Math.max(0, WORKER_SALARY - totalSalaryAndAdvance);
    },

    /**
     * Валидация суммы относительно лимитов
     */
    validateAmountLimits: function (amount, type) {
        var limits = {
            isValid: true,
            message: ''
        };

        // Проверка для зарплаты и аванса
        if (type === WORKER_FINANCES_TYPE_SALARY || type === WORKER_FINANCES_TYPE_ADVANCE) {
            var remainingSalary = this.calculateRemainingSalary();

            if (amount > remainingSalary) {
                limits.isValid = false;
                limits.message = 'Сумма превышает оставшуюся зарплату: ' + this.formatNumber(remainingSalary);
            }
        }

        // Проверка для погашения долга
        if (type === WORKER_FINANCES_TYPE_DEBT_PAYMENT) {
            var maxDebt = WORKER_DEBT || 0;

            if (amount > maxDebt) {
                limits.isValid = false;
                limits.message = 'Сумма превышает размер долга: ' + this.formatNumber(maxDebt);
            }
        }

        return limits;
    },

    /**
     * Форматирование валюты
     */
    formatCurrency: function (amount, currency = 'сум') {
        return this.formatNumber(amount) + ' ' + currency;
    },

    /**
     * Округление до копеек
     */
    roundToCents: function (amount) {
        return Math.round(amount * 100) / 100;
    },

    /************************************
     * ДОПОЛНИТЕЛЬНЫЕ ФУНКЦИИ (как в форме создания)
     ************************************/

    /**
     * Форматирование ввода чисел с разделителями тысяч
     */
    formatNumberInput: function (value) {
        if (value === null || value === undefined || value === '') return '';

        try {
            var numericValue = value.toString().replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue, 10).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    },

    /**
     * Простая валидация ввода без автоматического распределения
     */
    handleMainAmountInput: function (amountInput) {
        // Просто валидируем общую сумму без автоматического вмешательства
        this.validateTotalAmount();
    },

    /**
     * Простая обработка изменения динамических полей без автоматического распределения
     */
    simpleAutoDistribute: function (changedInput) {
        var paymentRow = changedInput.closest('tr');

        // Просто пересчитываем общую сумму и валидируем
        this.updateMainAmountFromDynamicInputs(paymentRow);
        this.validateTotalAmount();
    },



    /**
     * Обновляет основное поле суммы на основе значений в динамических полях
     */
    updateMainAmountFromDynamicInputs: function (paymentRow) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        var totalAmount = 0;

        dynamicInputs.each(function () {
            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
            totalAmount += amount;
        });

        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(totalAmount > 0 ? this.formatNumberInput(totalAmount.toString()) : '');
    },



    /**
     * Простая валидация общей суммы всех платежей
     */
    validateTotalAmount: function () {
        var totalAmount = 0;
        var hasErrors = false;

        // Считаем общую сумму всех типов платежей
        $('.payment-type-row').each(function () {
            var row = $(this);
            var typeId = row.data('type');

            // Для зарплаты и аванса проверяем лимиты
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                var rowTotal = 0;

                // Считаем сумму всех полей в строке
                row.find('input[inputmode="numeric"]').each(function () {
                    var value = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    rowTotal += value;
                });

                totalAmount += rowTotal;
            }
        });

        // Проверяем лимит для зарплаты и аванса
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

        // Показываем ошибку если превышен лимит
        if (totalAmount > maxAllowedAmount) {
            this.showValidationError('Общая сумма зарплаты и аванса не может превышать оклад: ' + this.formatNumber(maxAllowedAmount));
            hasErrors = true;
        } else {
            this.hideValidationError();
        }

        return !hasErrors;
    },

    /**
     * Показать ошибку валидации
     */
    showValidationError: function (message) {
        var errorContainer = $('#validation-error-container');
        if (!errorContainer.length) {
            errorContainer = $('<div id="validation-error-container" class="alert alert-danger" style="margin-top: 10px;"></div>');
            $('#worker-payment-edit-form').prepend(errorContainer);
        }
        errorContainer.html('<strong>Ошибка:</strong> ' + message).show();
    },

    /**
     * Скрыть ошибку валидации
     */
    hideValidationError: function () {
        $('#validation-error-container').hide();
    },

    /**
     * Устанавливает значение в поле без вызова событий
     */
    setValueSilently: function (input, value) {
        input.data('processing', true);
        input.val(value);
        setTimeout(function () {
            input.removeData('processing');
        }, 10);
    }
};
