/**
 * Модуль расчетов для формы создания платежа работнику
 * Содержит функции для расчетов, форматирования и обработки числовых данных
 */

// Глобальный объект для расчетов
window.WorkerPaymentCalculations = {

    /**
     * Основная функция расчета общей суммы
     */
    calculateTotal: function () {
        var total = 0;
        var hasSalaryOrAdvance = false;
        var self = this;

        // Calculate total from table rows (все выплаты)
        $('.payment-amount:not(:disabled)').each(function () {
            var amount = self.getNumericValue($(this).val());
            total += amount;

            // Проверяем, есть ли выплата зарплаты или аванса
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                hasSalaryOrAdvance = true;
            }
        });

        // Добавляем сумму погашения долга, если чекбокс отмечен
        if ($('#debt-payment-checkbox').is(':checked')) {
            var debtAmount = self.getNumericValue($('#debt-payment-amount').val());
            total += debtAmount;
        }

        // Пересчитываем оставшуюся зарплату
        this.updateRemainingSalary();
    },

    /**
     * Обновление оставшейся зарплаты
     */
    updateRemainingSalary: function () {
        // Получаем изначальную зарплату
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var self = this;

        // Считаем сумму выплат по зарплате и авансу (только текущие выплаты в форме)
        var currentSalaryAndAdvancePayments = 0;
        $('.payment-amount:not(:disabled)').each(function () {
            var amount = self.getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');

            // Учитываем только зарплату и аванс
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                currentSalaryAndAdvancePayments += amount;
            }
        });

        // Получаем уже выплаченную сумму (только зарплата и аванс, без текущих выплат)
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;

        // Общая выплаченная сумма = уже выплаченное + текущие выплаты зарплаты/аванса
        var totalPaidIncludingCurrent = originalTotalPaid + currentSalaryAndAdvancePayments;

        // Оставшаяся зарплата = изначальная зарплата - общая выплаченная сумма
        var remainingSalary = originalSalary - totalPaidIncludingCurrent;

        // Обновляем отображение только если есть информация о зарплате
        if ($('#remaining-salary-info').is(':visible')) {
            $('#worker-remaining-salary').text(this.formatNumber(Math.max(0, remainingSalary)));
        }
    },

    /**
     * Форматирование числа для отображения
     */
    formatNumber: function (num) {
        return parseFloat(num || 0).toLocaleString('ru-RU');
    },

    /**
     * Функция для форматирования ввода чисел с разделителями тысяч
     */
    formatNumberInput: function (value) {
        // Если значение пустое, null или undefined
        if (value === null || value === undefined || value === '') return '';

        try {
            // Убираем все символы кроме цифр
            var numericValue = value.toString().replace(/[^\d]/g, '');

            if (numericValue === '') return '';

            // Форматируем с разделителями тысяч
            return parseInt(numericValue).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    },

    /**
     * Функция для получения чистого числового значения из отформатированного поля
     */
    getNumericValue: function (formattedValue) {
        if (formattedValue === null || formattedValue === undefined || formattedValue === '') {
            return 0;
        }
        try {
            var numericString = formattedValue.toString().replace(/[^\d]/g, '');
            return parseInt(numericString || '0') || 0;
        } catch (e) {
            console.warn('Error parsing numeric value:', formattedValue, e);
            return 0;
        }
    },

    /**
     * Упрощенная функция автоматического распределения остатка
     */
    simpleAutoDistribute: function (changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Для типов платежей, отличных от зарплаты и аванса, не делаем автоматическое распределение
        // Пользователь должен сам вводить суммы в каждое поле
        if (paymentTypeId != WORKER_FINANCES_TYPE_SALARY && paymentTypeId != WORKER_FINANCES_TYPE_ADVANCE) {
            // Просто пересчитываем общую сумму на основе введенных значений
            this.updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Получаем доступную сумму для зарплаты и аванса
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var availableAmount = Math.max(0, originalSalary - originalTotalPaid);

        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        if (dynamicInputs.length < 2) return; // Должно быть минимум 2 поля

        // Получаем текущие значения всех полей
        var currentAmounts = [];
        var totalCurrentAmount = 0;
        var changedInputIndex = -1;

        dynamicInputs.each(function (index) {
            var amount = window.WorkerPaymentCalculations.getNumericValue($(this).val());
            currentAmounts.push(amount);
            totalCurrentAmount += amount;

            if ($(this).is(changedInput)) {
                changedInputIndex = index;
            }
        });

        // Проверяем, не превышает ли введенная сумма доступную
        var changedAmount = currentAmounts[changedInputIndex];
        if (changedAmount > availableAmount) {
            // Если превышает, устанавливаем максимально доступную сумму
            changedAmount = availableAmount;
            changedInput.val(availableAmount > 0 ? this.formatNumberInput(availableAmount.toString()) : '');

            // Очищаем все остальные поля
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });
            return;
        }

        // Если есть остаток, распределяем его на следующее доступное поле
        var remainingAmount = availableAmount - changedAmount;

        if (remainingAmount > 0) {
            // Находим следующее поле (по кругу)
            var nextIndex = (changedInputIndex + 1) % dynamicInputs.length;
            var nextInput = dynamicInputs.eq(nextIndex);

            // Очищаем все поля кроме измененного
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });

            // Устанавливаем остаток в следующее поле
            nextInput.val(this.formatNumberInput(remainingAmount.toString()));
        } else {
            // Если остатка нет, очищаем все остальные поля
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });
        }

        // Обновляем основное поле с общей суммой
        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(this.formatNumberInput(availableAmount.toString()));
    },

    /**
     * Обновляет основное поле суммы на основе значений в динамических полях
     */
    updateMainAmountFromDynamicInputs: function (paymentRow) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        var totalAmount = 0;

        dynamicInputs.each(function () {
            var amount = window.WorkerPaymentCalculations.getNumericValue($(this).val());
            totalAmount += amount;
        });

        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(totalAmount > 0 ? this.formatNumberInput(totalAmount.toString()) : '');
    },

    /**
     * Упрощенная функция для автоматического распределения между методами оплаты
     */
    autoDistributeToMethods: function (row) {
        var typeId = row.data('type');
        var remainingAmount = 0;

        // Получаем оставшуюся сумму для зарплаты
        if (typeId == WORKER_FINANCES_TYPE_SALARY) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            remainingAmount = Math.max(0, originalSalary - originalTotalPaid);
        }

        if (remainingAmount > 0) {
            var dynamicInputs = row.find('.dynamic-amounts input');
            if (dynamicInputs.length === 2) {
                // Если есть два поля (наличные и карта), распределяем остаток на карту
                var cashInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_CASH + ']"]');
                var cardInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_PAYMENT_CARD + ']"]');

                if (cashInput.length && cardInput.length) {
                    // Оставляем поле наличных пустым, весь остаток на карту
                    cashInput.val('');
                    cardInput.val(this.formatNumberInput(remainingAmount.toString()));
                }
            }
        }
    },

    /**
     * Обновление сумм платежей на основе данных работника
     */
    updatePaymentAmounts: function (payments, salary) {
        // Reset all amounts and uncheck all checkboxes
        $('.payment-type-checkbox').prop('checked', false).trigger('change');

        // Set salary amount if not fully paid
        var salaryPaid = payments[WORKER_FINANCES_TYPE_SALARY] || 0;
        var advancePaid = payments[WORKER_FINANCES_TYPE_ADVANCE] || 0;
        var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
        var remainingSalary = salary - totalSalaryAndAdvancePaid;

        if (remainingSalary > 0) {
            var salaryCheckbox = $('#payment_type_' + WORKER_FINANCES_TYPE_SALARY);
            salaryCheckbox.prop('checked', true).trigger('change');

            // Set default payment method and amount
            var salaryRow = salaryCheckbox.closest('tr');
            salaryRow.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_CASH + '"]').prop('checked', true).trigger('change'); // Default to cash
            salaryRow.find('.amount-input').val(this.formatNumberInput(remainingSalary.toString()));

            // НЕ активируем автоматически погашение долга - пользователь сам решает
            var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
            if (debt > 0 && $('#debt-payment-section').is(':visible')) {
                // Только показываем предупреждение, но не активируем автоматически
                window.WorkerPaymentUI.showDebtPaymentWarning();
            }
        }

        this.calculateTotal();
    },

    /**
     * Обработка ввода в основное поле суммы типа платежа
     */
    handleMainAmountInput: function (amountInput) {
        var paymentRow = amountInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');
        var enteredAmount = this.getNumericValue(amountInput.val());

        // Проверяем лимиты для зарплаты и аванса
        if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

            // Если введенная сумма превышает доступную, устанавливаем максимальную
            if (enteredAmount > maxAllowedAmount) {
                enteredAmount = maxAllowedAmount;
                amountInput.val(maxAllowedAmount > 0 ? this.formatNumberInput(maxAllowedAmount.toString()) : '');
            }
        }

        // Если есть выбранные методы оплаты, автоматически распределяем сумму
        var selectedMethods = paymentRow.find('.payment-method-checkbox:checked');
        var dynamicAmounts = paymentRow.find('.dynamic-amounts input');

        if (selectedMethods.length > 1 && dynamicAmounts.length > 1 && enteredAmount > 0) {
            // Распределяем сумму между методами оплаты
            this.distributeAmountToMethods(paymentRow, enteredAmount);
        } else if (selectedMethods.length === 1 && dynamicAmounts.length === 1 && enteredAmount > 0) {
            // Если выбран только один метод, устанавливаем всю сумму на него
            dynamicAmounts.first().val(this.formatNumberInput(enteredAmount.toString()));
        }
    },

    /**
     * Распределение суммы между методами оплаты
     */
    distributeAmountToMethods: function (paymentRow, totalAmount) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');

        if (dynamicInputs.length === 2) {
            // Если два метода - устанавливаем всю сумму на первый метод, второй оставляем пустым
            dynamicInputs.first().val(this.formatNumberInput(totalAmount.toString()));
            dynamicInputs.last().val('');
        } else if (dynamicInputs.length > 2) {
            // Если больше двух методов - устанавливаем всю сумму на первый
            dynamicInputs.first().val(this.formatNumberInput(totalAmount.toString()));
            dynamicInputs.slice(1).val('');
        }
    }
};
